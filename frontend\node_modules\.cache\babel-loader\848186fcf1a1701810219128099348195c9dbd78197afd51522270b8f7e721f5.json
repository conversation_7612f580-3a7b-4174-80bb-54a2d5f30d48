{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\components\\\\EnvironmentalChart.js\";\nimport { LineChart, Line, XAxis, YAxis, Tooltip, CartesianGrid, ResponsiveContainer, Legend } from \"recharts\";\n\n// Function to format timestamp for X-axis display\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatXAxisLabel = tickItem => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = label => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({\n  active,\n  payload,\n  label\n}) => {\n  if (active && payload && payload.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-tooltip\",\n      style: {\n        backgroundColor: \"white\",\n        padding: \"10px\",\n        border: \"1px solid #ccc\",\n        borderRadius: \"4px\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: \"0 0 5px 0\",\n          fontWeight: \"bold\"\n        },\n        children: formatTooltipLabel(label)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: \"2px 0\",\n          color: entry.color\n        },\n        children: `${entry.dataKey}: ${entry.value}${entry.dataKey === \"temperature\" ? \"°C\" : \"%\"}`\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n  return null;\n};\n_c = CustomTooltip;\nexport default function EnvironmentalChart({\n  data\n}) {\n  // Filter data for environmental sensors\n  const environmentalData = data.filter(item => item.sensor_type === 'environmental');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Environmental Sensors (Temperature & Humidity)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n      width: \"100%\",\n      height: 400,\n      children: /*#__PURE__*/_jsxDEV(LineChart, {\n        data: environmentalData,\n        margin: {\n          top: 5,\n          right: 30,\n          left: 20,\n          bottom: 5\n        },\n        children: [/*#__PURE__*/_jsxDEV(XAxis, {\n          dataKey: \"timestamp\",\n          tickFormatter: formatXAxisLabel,\n          angle: -45,\n          textAnchor: \"end\",\n          height: 60,\n          interval: \"preserveStartEnd\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n          stroke: \"#ccc\",\n          strokeDasharray: \"3 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          type: \"monotone\",\n          dataKey: \"temperature\",\n          stroke: \"#ff7300\",\n          strokeWidth: 2,\n          dot: {\n            fill: \"#ff7300\",\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"Temperature (\\xB0C)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          type: \"monotone\",\n          dataKey: \"humidity\",\n          stroke: \"#387908\",\n          strokeWidth: 2,\n          dot: {\n            fill: \"#387908\",\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"Humidity (%)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_c2 = EnvironmentalChart;\nvar _c, _c2;\n$RefreshReg$(_c, \"CustomTooltip\");\n$RefreshReg$(_c2, \"EnvironmentalChart\");", "map": {"version": 3, "names": ["Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Cartesian<PERSON><PERSON>", "ResponsiveContainer", "Legend", "jsxDEV", "_jsxDEV", "formatXAxisLabel", "tickItem", "date", "Date", "toLocaleTimeString", "hour", "minute", "hour12", "formatTooltipLabel", "label", "toLocaleString", "year", "month", "day", "second", "CustomTooltip", "active", "payload", "length", "className", "style", "backgroundColor", "padding", "border", "borderRadius", "boxShadow", "children", "margin", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "entry", "index", "color", "dataKey", "value", "_c", "EnvironmentalChart", "data", "environmentalData", "filter", "item", "sensor_type", "width", "height", "top", "right", "left", "bottom", "tick<PERSON><PERSON><PERSON><PERSON>", "angle", "textAnchor", "interval", "content", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "strokeWidth", "dot", "fill", "r", "name", "_c2", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/components/EnvironmentalChart.js"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>Axis,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Tooltip,\n  CartesianGrid,\n  Responsive<PERSON><PERSON><PERSON>,\n  <PERSON>,\n} from \"recharts\";\n\n// Function to format timestamp for X-axis display\nconst formatXAxisLabel = (tickItem) => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false,\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = (label) => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false,\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({ active, payload, label }) => {\n  if (active && payload && payload.length) {\n    return (\n      <div\n        className=\"custom-tooltip\"\n        style={{\n          backgroundColor: \"white\",\n          padding: \"10px\",\n          border: \"1px solid #ccc\",\n          borderRadius: \"4px\",\n          boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        }}\n      >\n        <p style={{ margin: \"0 0 5px 0\", fontWeight: \"bold\" }}>\n          {formatTooltipLabel(label)}\n        </p>\n        {payload.map((entry, index) => (\n          <p key={index} style={{ margin: \"2px 0\", color: entry.color }}>\n            {`${entry.dataKey}: ${entry.value}${\n              entry.dataKey === \"temperature\" ? \"°C\" : \"%\"\n            }`}\n          </p>\n        ))}\n      </div>\n    );\n  }\n  return null;\n};\n\nexport default function EnvironmentalChart({ data }) {\n  // Filter data for environmental sensors\n  const environmentalData = data.filter(item => item.sensor_type === 'environmental');\n\n  return (\n    <div className=\"chart-section\">\n      <h3>Environmental Sensors (Temperature & Humidity)</h3>\n      <ResponsiveContainer width=\"100%\" height={400}>\n        <LineChart\n          data={environmentalData}\n          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n        >\n          <XAxis\n            dataKey=\"timestamp\"\n            tickFormatter={formatXAxisLabel}\n            angle={-45}\n            textAnchor=\"end\"\n            height={60}\n            interval=\"preserveStartEnd\"\n          />\n          <YAxis />\n          <Tooltip content={<CustomTooltip />} />\n          <CartesianGrid stroke=\"#ccc\" strokeDasharray=\"3 3\" />\n          <Legend />\n          <Line\n            type=\"monotone\"\n            dataKey=\"temperature\"\n            stroke=\"#ff7300\"\n            strokeWidth={2}\n            dot={{ fill: \"#ff7300\", strokeWidth: 2, r: 4 }}\n            name=\"Temperature (°C)\"\n          />\n          <Line\n            type=\"monotone\"\n            dataKey=\"humidity\"\n            stroke=\"#387908\"\n            strokeWidth={2}\n            dot={{ fill: \"#387908\", strokeWidth: 2, r: 4 }}\n            name=\"Humidity (%)\"\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,MAAM,QACD,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;EACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,QAAQ,CAAC;EAC/B,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EACpC,MAAMP,IAAI,GAAG,IAAIC,IAAI,CAACM,KAAK,CAAC;EAC5B,OAAOP,IAAI,CAACQ,cAAc,CAAC,OAAO,EAAE;IAClCC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,SAAS;IACdR,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBQ,MAAM,EAAE,SAAS;IACjBP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMQ,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAER;AAAM,CAAC,KAAK;EACpD,IAAIO,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;IACvC,oBACEnB,OAAA;MACEoB,SAAS,EAAC,gBAAgB;MAC1BC,KAAK,EAAE;QACLC,eAAe,EAAE,OAAO;QACxBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEF3B,OAAA;QAAGqB,KAAK,EAAE;UAAEO,MAAM,EAAE,WAAW;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDlB,kBAAkB,CAACC,KAAK;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,EACHf,OAAO,CAACgB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACxBpC,OAAA;QAAeqB,KAAK,EAAE;UAAEO,MAAM,EAAE,OAAO;UAAES,KAAK,EAAEF,KAAK,CAACE;QAAM,CAAE;QAAAV,QAAA,EAC3D,GAAGQ,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACI,KAAK,GAC/BJ,KAAK,CAACG,OAAO,KAAK,aAAa,GAAG,IAAI,GAAG,GAAG;MAC5C,GAHIF,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIV,CACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EACA,OAAO,IAAI;AACb,CAAC;AAACO,EAAA,GA3BIxB,aAAa;AA6BnB,eAAe,SAASyB,kBAAkBA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACnD;EACA,MAAMC,iBAAiB,GAAGD,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,KAAK,eAAe,CAAC;EAEnF,oBACE9C,OAAA;IAAKoB,SAAS,EAAC,eAAe;IAAAO,QAAA,gBAC5B3B,OAAA;MAAA2B,QAAA,EAAI;IAA8C;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvDjC,OAAA,CAACH,mBAAmB;MAACkD,KAAK,EAAC,MAAM;MAACC,MAAM,EAAE,GAAI;MAAArB,QAAA,eAC5C3B,OAAA,CAACT,SAAS;QACRmD,IAAI,EAAEC,iBAAkB;QACxBf,MAAM,EAAE;UAAEqB,GAAG,EAAE,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAEnD3B,OAAA,CAACP,KAAK;UACJ6C,OAAO,EAAC,WAAW;UACnBe,aAAa,EAAEpD,gBAAiB;UAChCqD,KAAK,EAAE,CAAC,EAAG;UACXC,UAAU,EAAC,KAAK;UAChBP,MAAM,EAAE,EAAG;UACXQ,QAAQ,EAAC;QAAkB;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFjC,OAAA,CAACN,KAAK;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACTjC,OAAA,CAACL,OAAO;UAAC8D,OAAO,eAAEzD,OAAA,CAACgB,aAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvCjC,OAAA,CAACJ,aAAa;UAAC8D,MAAM,EAAC,MAAM;UAACC,eAAe,EAAC;QAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjC,OAAA,CAACF,MAAM;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVjC,OAAA,CAACR,IAAI;UACHoE,IAAI,EAAC,UAAU;UACftB,OAAO,EAAC,aAAa;UACrBoB,MAAM,EAAC,SAAS;UAChBG,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEF,WAAW,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC/CC,IAAI,EAAC;QAAkB;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFjC,OAAA,CAACR,IAAI;UACHoE,IAAI,EAAC,UAAU;UACftB,OAAO,EAAC,UAAU;UAClBoB,MAAM,EAAC,SAAS;UAChBG,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEF,WAAW,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC/CC,IAAI,EAAC;QAAc;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV;AAACiC,GAAA,GA5CuBzB,kBAAkB;AAAA,IAAAD,EAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAA3B,EAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}