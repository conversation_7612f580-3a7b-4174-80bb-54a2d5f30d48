{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport axios from \"axios\";\nimport Chart from \"./components/Chart\";\nimport EnvironmentalChart from \"./components/EnvironmentalChart\";\nimport AirQualityChart from \"./components/AirQualityChart\";\nimport MotionLightChart from \"./components/MotionLightChart\";\nimport PressureFlowChart from \"./components/PressureFlowChart\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [data, setData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const fetchData = async () => {\n    try {\n      if (!loading) setIsRefreshing(true); // Only show refreshing indicator after initial load\n      setError(null);\n      const response = await axios.get(\"http://localhost:5000/api/data\");\n      setData(response.data.reverse());\n      setLastUpdated(new Date());\n      setLoading(false);\n      setIsRefreshing(false);\n    } catch (err) {\n      console.error(\"Error fetching data:\", err);\n      setError(\"Failed to fetch sensor data\");\n      setLoading(false);\n      setIsRefreshing(false);\n    }\n  };\n  useEffect(() => {\n    // Fetch data immediately when component mounts\n    fetchData();\n\n    // Set up interval to fetch data every 5 seconds\n    const interval = setInterval(() => {\n      fetchData();\n    }, 5000);\n\n    // Cleanup interval on component unmount\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"IoT Sensor Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading sensor data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"IoT Sensor Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: \"red\"\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Data will automatically refresh every 5 seconds.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"IoT Sensor Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"10px\",\n          fontSize: \"14px\",\n          color: \"#666\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: isRefreshing ? \"🔄 Refreshing...\" : \"🔄 Auto-refreshing every 5 seconds\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: \"20px\"\n          },\n          children: [\"Last updated:\", \" \", lastUpdated ? lastUpdated.toLocaleTimeString() : \"Loading...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"4KPHLxWmu8ZhUl+mLTyu3/gCwks=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "Chart", "EnvironmentalChart", "AirQualityChart", "MotionLightChart", "PressureFlowChart", "jsxDEV", "_jsxDEV", "App", "_s", "data", "setData", "loading", "setLoading", "error", "setError", "lastUpdated", "setLastUpdated", "isRefreshing", "setIsRefreshing", "fetchData", "response", "get", "reverse", "Date", "err", "console", "interval", "setInterval", "clearInterval", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "marginBottom", "fontSize", "marginLeft", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/App.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport Chart from \"./components/Chart\";\r\nimport EnvironmentalChart from \"./components/EnvironmentalChart\";\r\nimport AirQualityChart from \"./components/AirQualityChart\";\r\nimport MotionLightChart from \"./components/MotionLightChart\";\r\nimport PressureFlowChart from \"./components/PressureFlowChart\";\r\nimport \"./App.css\";\r\n\r\nfunction App() {\r\n  const [data, setData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [lastUpdated, setLastUpdated] = useState(null);\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      if (!loading) setIsRefreshing(true); // Only show refreshing indicator after initial load\r\n      setError(null);\r\n      const response = await axios.get(\"http://localhost:5000/api/data\");\r\n      setData(response.data.reverse());\r\n      setLastUpdated(new Date());\r\n      setLoading(false);\r\n      setIsRefreshing(false);\r\n    } catch (err) {\r\n      console.error(\"Error fetching data:\", err);\r\n      setError(\"Failed to fetch sensor data\");\r\n      setLoading(false);\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Fetch data immediately when component mounts\r\n    fetchData();\r\n\r\n    // Set up interval to fetch data every 5 seconds\r\n    const interval = setInterval(() => {\r\n      fetchData();\r\n    }, 5000);\r\n\r\n    // Cleanup interval on component unmount\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"dashboard-container\">\r\n        <h2>IoT Sensor Dashboard</h2>\r\n        <div className=\"chart-container\">\r\n          <p>Loading sensor data...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"dashboard-container\">\r\n        <h2>IoT Sensor Dashboard</h2>\r\n        <div className=\"chart-container\">\r\n          <p style={{ color: \"red\" }}>{error}</p>\r\n          <p>Data will automatically refresh every 5 seconds.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"dashboard-container\">\r\n      <h2>IoT Sensor Dashboard</h2>\r\n      <div className=\"chart-container\">\r\n        <div style={{ marginBottom: \"10px\", fontSize: \"14px\", color: \"#666\" }}>\r\n          <span>\r\n            {isRefreshing\r\n              ? \"🔄 Refreshing...\"\r\n              : \"🔄 Auto-refreshing every 5 seconds\"}\r\n          </span>\r\n          <span style={{ marginLeft: \"20px\" }}>\r\n            Last updated:{\" \"}\r\n            {lastUpdated ? lastUpdated.toLocaleTimeString() : \"Loading...\"}\r\n          </span>\r\n        </div>\r\n        <Chart data={data} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMqB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,IAAI,CAACR,OAAO,EAAEO,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;MACrCJ,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMM,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,gCAAgC,CAAC;MAClEX,OAAO,CAACU,QAAQ,CAACX,IAAI,CAACa,OAAO,CAAC,CAAC,CAAC;MAChCN,cAAc,CAAC,IAAIO,IAAI,CAAC,CAAC,CAAC;MAC1BX,UAAU,CAAC,KAAK,CAAC;MACjBM,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZC,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEW,GAAG,CAAC;MAC1CV,QAAQ,CAAC,6BAA6B,CAAC;MACvCF,UAAU,CAAC,KAAK,CAAC;MACjBM,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACd;IACAsB,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMO,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCR,SAAS,CAAC,CAAC;IACb,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,OAAO,MAAMS,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIf,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKuB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCxB,OAAA;QAAAwB,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B5B,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BxB,OAAA;UAAAwB,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrB,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKuB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCxB,OAAA;QAAAwB,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7B5B,OAAA;QAAKuB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxB,OAAA;UAAG6B,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAM,CAAE;UAAAN,QAAA,EAAEjB;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvC5B,OAAA;UAAAwB,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5B,OAAA;IAAKuB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCxB,OAAA;MAAAwB,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7B5B,OAAA;MAAKuB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BxB,OAAA;QAAK6B,KAAK,EAAE;UAAEE,YAAY,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACpExB,OAAA;UAAAwB,QAAA,EACGb,YAAY,GACT,kBAAkB,GAClB;QAAoC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACP5B,OAAA;UAAM6B,KAAK,EAAE;YAAEI,UAAU,EAAE;UAAO,CAAE;UAAAT,QAAA,GAAC,eACtB,EAAC,GAAG,EAChBf,WAAW,GAAGA,WAAW,CAACyB,kBAAkB,CAAC,CAAC,GAAG,YAAY;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5B,OAAA,CAACN,KAAK;QAACS,IAAI,EAAEA;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1B,EAAA,CA/EQD,GAAG;AAAAkC,EAAA,GAAHlC,GAAG;AAiFZ,eAAeA,GAAG;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}