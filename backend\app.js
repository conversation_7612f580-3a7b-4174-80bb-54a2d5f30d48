const express = require("express");
const cors = require("cors");
const pool = require("./db");
require("./mqttHandler");

const app = express();
app.use(cors());

// Get all sensor data
app.get("/api/data", async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT * FROM sensor_data ORDER BY timestamp DESC LIMIT 100"
    );
    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching data:", err);
    res.status(500).json({ error: "Failed to fetch sensor data" });
  }
});

// Get data by sensor type
app.get("/api/data/:sensorType", async (req, res) => {
  try {
    const { sensorType } = req.params;
    const result = await pool.query(
      "SELECT * FROM sensor_data WHERE sensor_type = $1 ORDER BY timestamp DESC LIMIT 50",
      [sensorType]
    );
    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching sensor type data:", err);
    res.status(500).json({ error: "Failed to fetch sensor data" });
  }
});

// Get latest readings for all sensors
app.get("/api/latest", async (req, res) => {
  try {
    const result = await pool.query(
      "SELECT * FROM latest_sensor_readings ORDER BY device_id"
    );
    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching latest data:", err);
    res.status(500).json({ error: "Failed to fetch latest sensor data" });
  }
});

// Get sensor types and their counts
app.get("/api/sensors", async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        sensor_type,
        COUNT(DISTINCT device_id) as device_count,
        COUNT(*) as total_readings,
        MAX(timestamp) as last_reading
      FROM sensor_data
      GROUP BY sensor_type
      ORDER BY sensor_type
    `);
    res.json(result.rows);
  } catch (err) {
    console.error("Error fetching sensor info:", err);
    res.status(500).json({ error: "Failed to fetch sensor information" });
  }
});

app.listen(5000, () => console.log("Backend running on port 5000"));
