{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\components\\\\AirQualityChart.js\";\nimport { LineChart, Line, XAxis, YAxis, Tooltip, CartesianGrid, ResponsiveContainer, Legend, Composed<PERSON>hart, Bar } from \"recharts\";\n\n// Function to format timestamp for X-axis display\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatXAxisLabel = tickItem => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = label => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({\n  active,\n  payload,\n  label\n}) => {\n  if (active && payload && payload.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-tooltip\",\n      style: {\n        backgroundColor: \"white\",\n        padding: \"10px\",\n        border: \"1px solid #ccc\",\n        borderRadius: \"4px\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: \"0 0 5px 0\",\n          fontWeight: \"bold\"\n        },\n        children: formatTooltipLabel(label)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), payload.map((entry, index) => {\n        let unit = \"\";\n        if (entry.dataKey === \"co2_level\") unit = \" ppm\";else if (entry.dataKey === \"pm2_5\") unit = \" μg/m³\";else if (entry.dataKey === \"air_quality_index\") unit = \" AQI\";\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: \"2px 0\",\n            color: entry.color\n          },\n          children: `${entry.dataKey}: ${entry.value}${unit}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this);\n  }\n  return null;\n};\n_c = CustomTooltip;\nexport default function AirQualityChart({\n  data\n}) {\n  // Filter data for air quality sensors\n  const airQualityData = data.filter(item => item.sensor_type === 'air_quality');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Air Quality Sensors\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n      width: \"100%\",\n      height: 400,\n      children: /*#__PURE__*/_jsxDEV(ComposedChart, {\n        data: airQualityData,\n        margin: {\n          top: 5,\n          right: 30,\n          left: 20,\n          bottom: 5\n        },\n        children: [/*#__PURE__*/_jsxDEV(XAxis, {\n          dataKey: \"timestamp\",\n          tickFormatter: formatXAxisLabel,\n          angle: -45,\n          textAnchor: \"end\",\n          height: 60,\n          interval: \"preserveStartEnd\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n          yAxisId: \"left\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n          yAxisId: \"right\",\n          orientation: \"right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n          stroke: \"#ccc\",\n          strokeDasharray: \"3 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          yAxisId: \"left\",\n          type: \"monotone\",\n          dataKey: \"co2_level\",\n          stroke: \"#8884d8\",\n          strokeWidth: 2,\n          dot: {\n            fill: \"#8884d8\",\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"CO2 Level (ppm)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          yAxisId: \"left\",\n          type: \"monotone\",\n          dataKey: \"pm2_5\",\n          stroke: \"#82ca9d\",\n          strokeWidth: 2,\n          dot: {\n            fill: \"#82ca9d\",\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"PM2.5 (\\u03BCg/m\\xB3)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Bar, {\n          yAxisId: \"right\",\n          dataKey: \"air_quality_index\",\n          fill: \"#ffc658\",\n          name: \"Air Quality Index\",\n          opacity: 0.6\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}\n_c2 = AirQualityChart;\nvar _c, _c2;\n$RefreshReg$(_c, \"CustomTooltip\");\n$RefreshReg$(_c2, \"AirQualityChart\");", "map": {"version": 3, "names": ["Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Cartesian<PERSON><PERSON>", "ResponsiveContainer", "Legend", "ComposedChart", "Bar", "jsxDEV", "_jsxDEV", "formatXAxisLabel", "tickItem", "date", "Date", "toLocaleTimeString", "hour", "minute", "hour12", "formatTooltipLabel", "label", "toLocaleString", "year", "month", "day", "second", "CustomTooltip", "active", "payload", "length", "className", "style", "backgroundColor", "padding", "border", "borderRadius", "boxShadow", "children", "margin", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "entry", "index", "unit", "dataKey", "color", "value", "_c", "AirQualityChart", "data", "airQualityData", "filter", "item", "sensor_type", "width", "height", "top", "right", "left", "bottom", "tick<PERSON><PERSON><PERSON><PERSON>", "angle", "textAnchor", "interval", "yAxisId", "orientation", "content", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "strokeWidth", "dot", "fill", "r", "name", "opacity", "_c2", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/components/AirQualityChart.js"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  XAxis,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Tooltip,\n  CartesianGrid,\n  Responsive<PERSON>ontaine<PERSON>,\n  <PERSON>,\n  Composed<PERSON><PERSON>,\n  Bar,\n} from \"recharts\";\n\n// Function to format timestamp for X-axis display\nconst formatXAxisLabel = (tickItem) => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false,\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = (label) => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false,\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({ active, payload, label }) => {\n  if (active && payload && payload.length) {\n    return (\n      <div\n        className=\"custom-tooltip\"\n        style={{\n          backgroundColor: \"white\",\n          padding: \"10px\",\n          border: \"1px solid #ccc\",\n          borderRadius: \"4px\",\n          boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        }}\n      >\n        <p style={{ margin: \"0 0 5px 0\", fontWeight: \"bold\" }}>\n          {formatTooltipLabel(label)}\n        </p>\n        {payload.map((entry, index) => {\n          let unit = \"\";\n          if (entry.dataKey === \"co2_level\") unit = \" ppm\";\n          else if (entry.dataKey === \"pm2_5\") unit = \" μg/m³\";\n          else if (entry.dataKey === \"air_quality_index\") unit = \" AQI\";\n          \n          return (\n            <p key={index} style={{ margin: \"2px 0\", color: entry.color }}>\n              {`${entry.dataKey}: ${entry.value}${unit}`}\n            </p>\n          );\n        })}\n      </div>\n    );\n  }\n  return null;\n};\n\nexport default function AirQualityChart({ data }) {\n  // Filter data for air quality sensors\n  const airQualityData = data.filter(item => item.sensor_type === 'air_quality');\n\n  return (\n    <div className=\"chart-section\">\n      <h3>Air Quality Sensors</h3>\n      <ResponsiveContainer width=\"100%\" height={400}>\n        <ComposedChart\n          data={airQualityData}\n          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n        >\n          <XAxis\n            dataKey=\"timestamp\"\n            tickFormatter={formatXAxisLabel}\n            angle={-45}\n            textAnchor=\"end\"\n            height={60}\n            interval=\"preserveStartEnd\"\n          />\n          <YAxis yAxisId=\"left\" />\n          <YAxis yAxisId=\"right\" orientation=\"right\" />\n          <Tooltip content={<CustomTooltip />} />\n          <CartesianGrid stroke=\"#ccc\" strokeDasharray=\"3 3\" />\n          <Legend />\n          <Line\n            yAxisId=\"left\"\n            type=\"monotone\"\n            dataKey=\"co2_level\"\n            stroke=\"#8884d8\"\n            strokeWidth={2}\n            dot={{ fill: \"#8884d8\", strokeWidth: 2, r: 4 }}\n            name=\"CO2 Level (ppm)\"\n          />\n          <Line\n            yAxisId=\"left\"\n            type=\"monotone\"\n            dataKey=\"pm2_5\"\n            stroke=\"#82ca9d\"\n            strokeWidth={2}\n            dot={{ fill: \"#82ca9d\", strokeWidth: 2, r: 4 }}\n            name=\"PM2.5 (μg/m³)\"\n          />\n          <Bar\n            yAxisId=\"right\"\n            dataKey=\"air_quality_index\"\n            fill=\"#ffc658\"\n            name=\"Air Quality Index\"\n            opacity={0.6}\n          />\n        </ComposedChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,MAAM,EACNC,aAAa,EACbC,GAAG,QACE,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;EACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,QAAQ,CAAC;EAC/B,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EACpC,MAAMP,IAAI,GAAG,IAAIC,IAAI,CAACM,KAAK,CAAC;EAC5B,OAAOP,IAAI,CAACQ,cAAc,CAAC,OAAO,EAAE;IAClCC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,SAAS;IACdR,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBQ,MAAM,EAAE,SAAS;IACjBP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMQ,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAER;AAAM,CAAC,KAAK;EACpD,IAAIO,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;IACvC,oBACEnB,OAAA;MACEoB,SAAS,EAAC,gBAAgB;MAC1BC,KAAK,EAAE;QACLC,eAAe,EAAE,OAAO;QACxBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEF3B,OAAA;QAAGqB,KAAK,EAAE;UAAEO,MAAM,EAAE,WAAW;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDlB,kBAAkB,CAACC,KAAK;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,EACHf,OAAO,CAACgB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC7B,IAAIC,IAAI,GAAG,EAAE;QACb,IAAIF,KAAK,CAACG,OAAO,KAAK,WAAW,EAAED,IAAI,GAAG,MAAM,CAAC,KAC5C,IAAIF,KAAK,CAACG,OAAO,KAAK,OAAO,EAAED,IAAI,GAAG,QAAQ,CAAC,KAC/C,IAAIF,KAAK,CAACG,OAAO,KAAK,mBAAmB,EAAED,IAAI,GAAG,MAAM;QAE7D,oBACErC,OAAA;UAAeqB,KAAK,EAAE;YAAEO,MAAM,EAAE,OAAO;YAAEW,KAAK,EAAEJ,KAAK,CAACI;UAAM,CAAE;UAAAZ,QAAA,EAC3D,GAAGQ,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACK,KAAK,GAAGH,IAAI;QAAE,GADpCD,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAER,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EACA,OAAO,IAAI;AACb,CAAC;AAACQ,EAAA,GAhCIzB,aAAa;AAkCnB,eAAe,SAAS0B,eAAeA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAChD;EACA,MAAMC,cAAc,GAAGD,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,KAAK,aAAa,CAAC;EAE9E,oBACE/C,OAAA;IAAKoB,SAAS,EAAC,eAAe;IAAAO,QAAA,gBAC5B3B,OAAA;MAAA2B,QAAA,EAAI;IAAmB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5BjC,OAAA,CAACL,mBAAmB;MAACqD,KAAK,EAAC,MAAM;MAACC,MAAM,EAAE,GAAI;MAAAtB,QAAA,eAC5C3B,OAAA,CAACH,aAAa;QACZ8C,IAAI,EAAEC,cAAe;QACrBhB,MAAM,EAAE;UAAEsB,GAAG,EAAE,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAA1B,QAAA,gBAEnD3B,OAAA,CAACT,KAAK;UACJ+C,OAAO,EAAC,WAAW;UACnBgB,aAAa,EAAErD,gBAAiB;UAChCsD,KAAK,EAAE,CAAC,EAAG;UACXC,UAAU,EAAC,KAAK;UAChBP,MAAM,EAAE,EAAG;UACXQ,QAAQ,EAAC;QAAkB;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFjC,OAAA,CAACR,KAAK;UAACkE,OAAO,EAAC;QAAM;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBjC,OAAA,CAACR,KAAK;UAACkE,OAAO,EAAC,OAAO;UAACC,WAAW,EAAC;QAAO;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CjC,OAAA,CAACP,OAAO;UAACmE,OAAO,eAAE5D,OAAA,CAACgB,aAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvCjC,OAAA,CAACN,aAAa;UAACmE,MAAM,EAAC,MAAM;UAACC,eAAe,EAAC;QAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjC,OAAA,CAACJ,MAAM;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVjC,OAAA,CAACV,IAAI;UACHoE,OAAO,EAAC,MAAM;UACdK,IAAI,EAAC,UAAU;UACfzB,OAAO,EAAC,WAAW;UACnBuB,MAAM,EAAC,SAAS;UAChBG,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEF,WAAW,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC/CC,IAAI,EAAC;QAAiB;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFjC,OAAA,CAACV,IAAI;UACHoE,OAAO,EAAC,MAAM;UACdK,IAAI,EAAC,UAAU;UACfzB,OAAO,EAAC,OAAO;UACfuB,MAAM,EAAC,SAAS;UAChBG,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEF,WAAW,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC/CC,IAAI,EAAC;QAAe;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFjC,OAAA,CAACF,GAAG;UACF4D,OAAO,EAAC,OAAO;UACfpB,OAAO,EAAC,mBAAmB;UAC3B4B,IAAI,EAAC,SAAS;UACdE,IAAI,EAAC,mBAAmB;UACxBC,OAAO,EAAE;QAAI;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV;AAACqC,GAAA,GAtDuB5B,eAAe;AAAA,IAAAD,EAAA,EAAA6B,GAAA;AAAAC,YAAA,CAAA9B,EAAA;AAAA8B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}