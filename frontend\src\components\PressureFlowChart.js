import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  Tooltip,
  CartesianGrid,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON>,
  Composed<PERSON><PERSON>,
  Bar,
} from "recharts";

// Function to format timestamp for X-axis display
const formatXAxisLabel = (tickItem) => {
  const date = new Date(tickItem);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

// Function to format timestamp for tooltip
const formatTooltipLabel = (label) => {
  const date = new Date(label);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: "white",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <p style={{ margin: "0 0 5px 0", fontWeight: "bold" }}>
          {formatTooltipLabel(label)}
        </p>
        {payload.map((entry, index) => {
          let unit = "";
          if (entry.dataKey === "pressure") unit = " bar";
          else if (entry.dataKey === "flow_rate") unit = " L/min";
          else if (entry.dataKey === "vibration") unit = " mm/s";
          
          return (
            <p key={index} style={{ margin: "2px 0", color: entry.color }}>
              {`${entry.dataKey}: ${entry.value}${unit}`}
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

// Function to get status color
const getStatusColor = (status) => {
  switch (status) {
    case 'normal': return '#4CAF50';
    case 'warning': return '#FF9800';
    case 'critical': return '#F44336';
    default: return '#9E9E9E';
  }
};

export default function PressureFlowChart({ data }) {
  // Filter data for pressure/flow sensors
  const pressureFlowData = data.filter(item => item.sensor_type === 'pressure_flow');

  return (
    <div className="chart-section">
      <h3>Pressure & Flow Sensors</h3>
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart
          data={pressureFlowData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <XAxis
            dataKey="timestamp"
            tickFormatter={formatXAxisLabel}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip content={<CustomTooltip />} />
          <CartesianGrid stroke="#ccc" strokeDasharray="3 3" />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="pressure"
            stroke="#2196F3"
            strokeWidth={2}
            dot={{ fill: "#2196F3", strokeWidth: 2, r: 4 }}
            name="Pressure (bar)"
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="flow_rate"
            stroke="#9C27B0"
            strokeWidth={2}
            dot={{ fill: "#9C27B0", strokeWidth: 2, r: 4 }}
            name="Flow Rate (L/min)"
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="vibration"
            stroke="#FF5722"
            strokeWidth={2}
            dot={{ fill: "#FF5722", strokeWidth: 2, r: 4 }}
            name="Vibration (mm/s)"
          />
        </ComposedChart>
      </ResponsiveContainer>
      
      {/* Status indicators */}
      <div style={{ marginTop: '10px', display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
        {pressureFlowData.slice(-5).map((item, index) => (
          <div 
            key={index}
            style={{
              padding: '5px 10px',
              borderRadius: '4px',
              backgroundColor: getStatusColor(item.status),
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
          >
            {item.device_id}: {item.status}
          </div>
        ))}
      </div>
    </div>
  );
}
