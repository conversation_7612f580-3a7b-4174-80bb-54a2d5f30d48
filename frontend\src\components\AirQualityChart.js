import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  Tooltip,
  CartesianGrid,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON>,
  Composed<PERSON><PERSON>,
  Bar,
} from "recharts";

// Function to format timestamp for X-axis display
const formatXAxisLabel = (tickItem) => {
  const date = new Date(tickItem);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

// Function to format timestamp for tooltip
const formatTooltipLabel = (label) => {
  const date = new Date(label);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: "white",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <p style={{ margin: "0 0 5px 0", fontWeight: "bold" }}>
          {formatTooltipLabel(label)}
        </p>
        {payload.map((entry, index) => {
          let unit = "";
          if (entry.dataKey === "co2_level") unit = " ppm";
          else if (entry.dataKey === "pm2_5") unit = " μg/m³";
          else if (entry.dataKey === "air_quality_index") unit = " AQI";
          
          return (
            <p key={index} style={{ margin: "2px 0", color: entry.color }}>
              {`${entry.dataKey}: ${entry.value}${unit}`}
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

export default function AirQualityChart({ data }) {
  // Filter data for air quality sensors
  const airQualityData = data.filter(item => item.sensor_type === 'air_quality');

  return (
    <div className="chart-section">
      <h3>Air Quality Sensors</h3>
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart
          data={airQualityData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <XAxis
            dataKey="timestamp"
            tickFormatter={formatXAxisLabel}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip content={<CustomTooltip />} />
          <CartesianGrid stroke="#ccc" strokeDasharray="3 3" />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="co2_level"
            stroke="#8884d8"
            strokeWidth={2}
            dot={{ fill: "#8884d8", strokeWidth: 2, r: 4 }}
            name="CO2 Level (ppm)"
          />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="pm2_5"
            stroke="#82ca9d"
            strokeWidth={2}
            dot={{ fill: "#82ca9d", strokeWidth: 2, r: 4 }}
            name="PM2.5 (μg/m³)"
          />
          <Bar
            yAxisId="right"
            dataKey="air_quality_index"
            fill="#ffc658"
            name="Air Quality Index"
            opacity={0.6}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
}
