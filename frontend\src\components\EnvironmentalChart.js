import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  Tooltip,
  CartesianGrid,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";

// Function to format timestamp for X-axis display
const formatXAxisLabel = (tickItem) => {
  const date = new Date(tickItem);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

// Function to format timestamp for tooltip
const formatTooltipLabel = (label) => {
  const date = new Date(label);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: "white",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <p style={{ margin: "0 0 5px 0", fontWeight: "bold" }}>
          {formatTooltipLabel(label)}
        </p>
        {payload.map((entry, index) => (
          <p key={index} style={{ margin: "2px 0", color: entry.color }}>
            {`${entry.dataKey}: ${entry.value}${
              entry.dataKey === "temperature" ? "°C" : "%"
            }`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export default function EnvironmentalChart({ data }) {
  // Filter data for environmental sensors
  const environmentalData = data.filter(item => item.sensor_type === 'environmental');

  return (
    <div className="chart-section">
      <h3>Environmental Sensors (Temperature & Humidity)</h3>
      <ResponsiveContainer width="100%" height={400}>
        <LineChart
          data={environmentalData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <XAxis
            dataKey="timestamp"
            tickFormatter={formatXAxisLabel}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis />
          <Tooltip content={<CustomTooltip />} />
          <CartesianGrid stroke="#ccc" strokeDasharray="3 3" />
          <Legend />
          <Line
            type="monotone"
            dataKey="temperature"
            stroke="#ff7300"
            strokeWidth={2}
            dot={{ fill: "#ff7300", strokeWidth: 2, r: 4 }}
            name="Temperature (°C)"
          />
          <Line
            type="monotone"
            dataKey="humidity"
            stroke="#387908"
            strokeWidth={2}
            dot={{ fill: "#387908", strokeWidth: 2, r: 4 }}
            name="Humidity (%)"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
