-- Migration script to update sensor_data table for multiple sensor types
-- Run this script to update your existing database

-- First, let's backup the existing table structure
-- CREATE TABLE sensor_data_backup AS SELECT * FROM sensor_data;

-- Drop the existing table (be careful - this will lose existing data)
-- If you want to preserve data, you'll need to modify this approach
DROP TABLE IF EXISTS sensor_data;

-- Create new comprehensive sensor_data table
CREATE TABLE sensor_data (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    sensor_type VARCHAR(50) NOT NULL,
    location VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Environmental sensor data (sensor-001)
    temperature DECIMAL(5,2),
    humidity DECIMAL(5,2),
    
    -- Air quality sensor data (sensor-002)
    co2_level DECIMAL(6,1),
    pm2_5 DECIMAL(5,2),
    air_quality_index INTEGER,
    
    -- Motion and light sensor data (sensor-003)
    motion_detected BOOLEAN,
    light_level DECIMAL(6,1),
    battery_level DECIMAL(5,1),
    
    -- Pressure and flow sensor data (sensor-004)
    pressure DECIMAL(5,2),
    flow_rate DECIMAL(5,1),
    vibration DECIMAL(6,3),
    status VARCHAR(20),
    
    -- Additional metadata
    raw_data JSONB -- Store complete raw sensor data for flexibility
);

-- Create indexes for better query performance
CREATE INDEX idx_sensor_data_device_id ON sensor_data(device_id);
CREATE INDEX idx_sensor_data_type ON sensor_data(sensor_type);
CREATE INDEX idx_sensor_data_timestamp ON sensor_data(timestamp);
CREATE INDEX idx_sensor_data_location ON sensor_data(location);

-- Create a view for easy querying of latest sensor readings
CREATE OR REPLACE VIEW latest_sensor_readings AS
SELECT DISTINCT ON (device_id) 
    device_id,
    sensor_type,
    location,
    timestamp,
    temperature,
    humidity,
    co2_level,
    pm2_5,
    air_quality_index,
    motion_detected,
    light_level,
    battery_level,
    pressure,
    flow_rate,
    vibration,
    status
FROM sensor_data 
ORDER BY device_id, timestamp DESC;

-- Insert some sample data for testing (optional)
-- INSERT INTO sensor_data (device_id, sensor_type, location, temperature, humidity) 
-- VALUES ('sensor-001', 'environmental', 'office', 23.5, 45.2);
