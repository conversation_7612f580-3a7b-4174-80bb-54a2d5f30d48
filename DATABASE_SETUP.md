# Database Setup Instructions

## Prerequisites
- PostgreSQL installed and running
- Access to your PostgreSQL database (iotdb)

## Migration Steps

### Step 1: Backup Existing Data (Optional but Recommended)
```sql
-- Connect to your database first
\c iotdb

-- Create a backup of existing data
CREATE TABLE sensor_data_backup AS SELECT * FROM sensor_data;
```

### Step 2: Run the Migration Script
```bash
# Navigate to the backend directory
cd backend

# Run the migration script
psql -U nehalkapadia -d iotdb -p 5433 -f migrate_database.sql
```

### Step 3: Verify the Migration
```sql
-- Check the new table structure
\d sensor_data

-- Check if the view was created
\d latest_sensor_readings

-- Verify indexes
\di sensor_data*
```

## New Database Schema

The updated `sensor_data` table now includes:

### Common Fields
- `id` - Primary key (auto-increment)
- `device_id` - Sensor device identifier
- `sensor_type` - Type of sensor (environmental, air_quality, motion_light, pressure_flow)
- `location` - Physical location of the sensor
- `timestamp` - When the data was recorded
- `raw_data` - Complete JSON data for flexibility

### Environmental Sensors (sensor-001)
- `temperature` - Temperature in Celsius
- `humidity` - Humidity percentage

### Air Quality Sensors (sensor-002)
- `co2_level` - CO2 concentration in ppm
- `pm2_5` - PM2.5 particles in μg/m³
- `air_quality_index` - Air quality index value

### Motion & Light Sensors (sensor-003)
- `motion_detected` - Boolean for motion detection
- `light_level` - Light level in lux
- `battery_level` - Battery percentage

### Pressure & Flow Sensors (sensor-004)
- `pressure` - Pressure in bar
- `flow_rate` - Flow rate in L/min
- `vibration` - Vibration in mm/s
- `status` - Operational status (normal/warning/critical)

## API Endpoints

The backend now provides these endpoints:

- `GET /api/data` - All sensor data (last 100 records)
- `GET /api/data/:sensorType` - Data for specific sensor type
- `GET /api/latest` - Latest reading from each sensor
- `GET /api/sensors` - Sensor statistics and counts

## Testing the Setup

1. **Start the simulator**: `python simulator/publisher.py`
2. **Start the backend**: `npm start` (in backend directory)
3. **Start the frontend**: `npm start` (in frontend directory)
4. **Check the database**: Verify data is being inserted with the new structure

## Troubleshooting

### If migration fails:
1. Check PostgreSQL connection settings in `backend/db.js`
2. Ensure the database user has proper permissions
3. Verify the database name and port are correct

### If no data appears:
1. Check MQTT broker connection in `backend/mqttHandler.js`
2. Verify the simulator is publishing data
3. Check backend console for error messages

### If charts don't display:
1. Verify the frontend is fetching data from the correct API endpoints
2. Check browser console for JavaScript errors
3. Ensure all chart components are properly imported
