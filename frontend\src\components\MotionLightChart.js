import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YA<PERSON>s,
  Tooltip,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON>,
  Composed<PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";

// Function to format timestamp for X-axis display
const formatXAxisLabel = (tickItem) => {
  const date = new Date(tickItem);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

// Function to format timestamp for tooltip
const formatTooltipLabel = (label) => {
  const date = new Date(label);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: "white",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <p style={{ margin: "0 0 5px 0", fontWeight: "bold" }}>
          {formatTooltipLabel(label)}
        </p>
        {payload.map((entry, index) => {
          let unit = "";
          let value = entry.value;
          if (entry.dataKey === "light_level") unit = " lux";
          else if (entry.dataKey === "battery_level") unit = "%";
          else if (entry.dataKey === "motion_detected") {
            value = value ? "Yes" : "No";
            unit = "";
          }
          
          return (
            <p key={index} style={{ margin: "2px 0", color: entry.color }}>
              {`${entry.dataKey}: ${value}${unit}`}
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

export default function MotionLightChart({ data }) {
  // Filter data for motion/light sensors
  const motionLightData = data.filter(item => item.sensor_type === 'motion_light');
  
  // Convert motion_detected boolean to numeric for charting
  const chartData = motionLightData.map(item => ({
    ...item,
    motion_numeric: item.motion_detected ? 1 : 0
  }));

  return (
    <div className="chart-section">
      <h3>Motion & Light Sensors</h3>
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart
          data={chartData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <XAxis
            dataKey="timestamp"
            tickFormatter={formatXAxisLabel}
            angle={-45}
            textAnchor="end"
            height={60}
            interval="preserveStartEnd"
          />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" domain={[0, 1]} />
          <Tooltip content={<CustomTooltip />} />
          <CartesianGrid stroke="#ccc" strokeDasharray="3 3" />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="light_level"
            stroke="#ffb347"
            strokeWidth={2}
            dot={{ fill: "#ffb347", strokeWidth: 2, r: 4 }}
            name="Light Level (lux)"
          />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="battery_level"
            stroke="#32cd32"
            strokeWidth={2}
            dot={{ fill: "#32cd32", strokeWidth: 2, r: 4 }}
            name="Battery Level (%)"
          />
          <Bar
            yAxisId="right"
            dataKey="motion_numeric"
            fill="#ff6b6b"
            name="Motion Detected"
            opacity={0.7}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
}
