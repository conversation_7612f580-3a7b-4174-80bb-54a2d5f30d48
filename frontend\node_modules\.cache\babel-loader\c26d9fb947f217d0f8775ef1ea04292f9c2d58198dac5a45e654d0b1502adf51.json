{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\components\\\\MotionLightChart.js\";\nimport { LineChart, Line, XAxis, YAxis, Tooltip, CartesianGrid, Responsive<PERSON>ontainer, <PERSON>, Composed<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"recharts\";\n\n// Function to format timestamp for X-axis display\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatXAxisLabel = tickItem => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = label => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({\n  active,\n  payload,\n  label\n}) => {\n  if (active && payload && payload.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-tooltip\",\n      style: {\n        backgroundColor: \"white\",\n        padding: \"10px\",\n        border: \"1px solid #ccc\",\n        borderRadius: \"4px\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: \"0 0 5px 0\",\n          fontWeight: \"bold\"\n        },\n        children: formatTooltipLabel(label)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), payload.map((entry, index) => {\n        let unit = \"\";\n        let value = entry.value;\n        if (entry.dataKey === \"light_level\") unit = \" lux\";else if (entry.dataKey === \"battery_level\") unit = \"%\";else if (entry.dataKey === \"motion_detected\") {\n          value = value ? \"Yes\" : \"No\";\n          unit = \"\";\n        }\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: \"2px 0\",\n            color: entry.color\n          },\n          children: `${entry.dataKey}: ${value}${unit}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return null;\n};\n_c = CustomTooltip;\nexport default function MotionLightChart({\n  data\n}) {\n  // Filter data for motion/light sensors\n  const motionLightData = data.filter(item => item.sensor_type === 'motion_light');\n\n  // Convert motion_detected boolean to numeric for charting\n  const chartData = motionLightData.map(item => ({\n    ...item,\n    motion_numeric: item.motion_detected ? 1 : 0\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Motion & Light Sensors\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n      width: \"100%\",\n      height: 400,\n      children: /*#__PURE__*/_jsxDEV(ComposedChart, {\n        data: chartData,\n        margin: {\n          top: 5,\n          right: 30,\n          left: 20,\n          bottom: 5\n        },\n        children: [/*#__PURE__*/_jsxDEV(XAxis, {\n          dataKey: \"timestamp\",\n          tickFormatter: formatXAxisLabel,\n          angle: -45,\n          textAnchor: \"end\",\n          height: 60,\n          interval: \"preserveStartEnd\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n          yAxisId: \"left\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n          yAxisId: \"right\",\n          orientation: \"right\",\n          domain: [0, 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n          stroke: \"#ccc\",\n          strokeDasharray: \"3 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          yAxisId: \"left\",\n          type: \"monotone\",\n          dataKey: \"light_level\",\n          stroke: \"#ffb347\",\n          strokeWidth: 2,\n          dot: {\n            fill: \"#ffb347\",\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"Light Level (lux)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Line, {\n          yAxisId: \"left\",\n          type: \"monotone\",\n          dataKey: \"battery_level\",\n          stroke: \"#32cd32\",\n          strokeWidth: 2,\n          dot: {\n            fill: \"#32cd32\",\n            strokeWidth: 2,\n            r: 4\n          },\n          name: \"Battery Level (%)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Bar, {\n          yAxisId: \"right\",\n          dataKey: \"motion_numeric\",\n          fill: \"#ff6b6b\",\n          name: \"Motion Detected\",\n          opacity: 0.7\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_c2 = MotionLightChart;\nvar _c, _c2;\n$RefreshReg$(_c, \"CustomTooltip\");\n$RefreshReg$(_c2, \"MotionLightChart\");", "map": {"version": 3, "names": ["Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Cartesian<PERSON><PERSON>", "ResponsiveContainer", "Legend", "ComposedChart", "Bar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "formatXAxisLabel", "tickItem", "date", "Date", "toLocaleTimeString", "hour", "minute", "hour12", "formatTooltipLabel", "label", "toLocaleString", "year", "month", "day", "second", "CustomTooltip", "active", "payload", "length", "className", "style", "backgroundColor", "padding", "border", "borderRadius", "boxShadow", "children", "margin", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "entry", "index", "unit", "value", "dataKey", "color", "_c", "MotionLightChart", "data", "motionLightData", "filter", "item", "sensor_type", "chartData", "motion_numeric", "motion_detected", "width", "height", "top", "right", "left", "bottom", "tick<PERSON><PERSON><PERSON><PERSON>", "angle", "textAnchor", "interval", "yAxisId", "orientation", "domain", "content", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "strokeWidth", "dot", "fill", "r", "name", "opacity", "_c2", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/components/MotionLightChart.js"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  XAxis,\n  YA<PERSON>s,\n  Tooltip,\n  CartesianGrid,\n  ResponsiveContainer,\n  <PERSON>,\n  Composed<PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n} from \"recharts\";\n\n// Function to format timestamp for X-axis display\nconst formatXAxisLabel = (tickItem) => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false,\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = (label) => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false,\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({ active, payload, label }) => {\n  if (active && payload && payload.length) {\n    return (\n      <div\n        className=\"custom-tooltip\"\n        style={{\n          backgroundColor: \"white\",\n          padding: \"10px\",\n          border: \"1px solid #ccc\",\n          borderRadius: \"4px\",\n          boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        }}\n      >\n        <p style={{ margin: \"0 0 5px 0\", fontWeight: \"bold\" }}>\n          {formatTooltipLabel(label)}\n        </p>\n        {payload.map((entry, index) => {\n          let unit = \"\";\n          let value = entry.value;\n          if (entry.dataKey === \"light_level\") unit = \" lux\";\n          else if (entry.dataKey === \"battery_level\") unit = \"%\";\n          else if (entry.dataKey === \"motion_detected\") {\n            value = value ? \"Yes\" : \"No\";\n            unit = \"\";\n          }\n          \n          return (\n            <p key={index} style={{ margin: \"2px 0\", color: entry.color }}>\n              {`${entry.dataKey}: ${value}${unit}`}\n            </p>\n          );\n        })}\n      </div>\n    );\n  }\n  return null;\n};\n\nexport default function MotionLightChart({ data }) {\n  // Filter data for motion/light sensors\n  const motionLightData = data.filter(item => item.sensor_type === 'motion_light');\n  \n  // Convert motion_detected boolean to numeric for charting\n  const chartData = motionLightData.map(item => ({\n    ...item,\n    motion_numeric: item.motion_detected ? 1 : 0\n  }));\n\n  return (\n    <div className=\"chart-section\">\n      <h3>Motion & Light Sensors</h3>\n      <ResponsiveContainer width=\"100%\" height={400}>\n        <ComposedChart\n          data={chartData}\n          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\n        >\n          <XAxis\n            dataKey=\"timestamp\"\n            tickFormatter={formatXAxisLabel}\n            angle={-45}\n            textAnchor=\"end\"\n            height={60}\n            interval=\"preserveStartEnd\"\n          />\n          <YAxis yAxisId=\"left\" />\n          <YAxis yAxisId=\"right\" orientation=\"right\" domain={[0, 1]} />\n          <Tooltip content={<CustomTooltip />} />\n          <CartesianGrid stroke=\"#ccc\" strokeDasharray=\"3 3\" />\n          <Legend />\n          <Line\n            yAxisId=\"left\"\n            type=\"monotone\"\n            dataKey=\"light_level\"\n            stroke=\"#ffb347\"\n            strokeWidth={2}\n            dot={{ fill: \"#ffb347\", strokeWidth: 2, r: 4 }}\n            name=\"Light Level (lux)\"\n          />\n          <Line\n            yAxisId=\"left\"\n            type=\"monotone\"\n            dataKey=\"battery_level\"\n            stroke=\"#32cd32\"\n            strokeWidth={2}\n            dot={{ fill: \"#32cd32\", strokeWidth: 2, r: 4 }}\n            name=\"Battery Level (%)\"\n          />\n          <Bar\n            yAxisId=\"right\"\n            dataKey=\"motion_numeric\"\n            fill=\"#ff6b6b\"\n            name=\"Motion Detected\"\n            opacity={0.7}\n          />\n        </ComposedChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,MAAM,EACNC,aAAa,EACbC,GAAG,EACHC,YAAY,EACZC,OAAO,QACF,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;EACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,QAAQ,CAAC;EAC/B,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EACpC,MAAMP,IAAI,GAAG,IAAIC,IAAI,CAACM,KAAK,CAAC;EAC5B,OAAOP,IAAI,CAACQ,cAAc,CAAC,OAAO,EAAE;IAClCC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,SAAS;IACdR,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBQ,MAAM,EAAE,SAAS;IACjBP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMQ,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAER;AAAM,CAAC,KAAK;EACpD,IAAIO,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;IACvC,oBACEnB,OAAA;MACEoB,SAAS,EAAC,gBAAgB;MAC1BC,KAAK,EAAE;QACLC,eAAe,EAAE,OAAO;QACxBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEF3B,OAAA;QAAGqB,KAAK,EAAE;UAAEO,MAAM,EAAE,WAAW;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDlB,kBAAkB,CAACC,KAAK;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,EACHf,OAAO,CAACgB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC7B,IAAIC,IAAI,GAAG,EAAE;QACb,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;QACvB,IAAIH,KAAK,CAACI,OAAO,KAAK,aAAa,EAAEF,IAAI,GAAG,MAAM,CAAC,KAC9C,IAAIF,KAAK,CAACI,OAAO,KAAK,eAAe,EAAEF,IAAI,GAAG,GAAG,CAAC,KAClD,IAAIF,KAAK,CAACI,OAAO,KAAK,iBAAiB,EAAE;UAC5CD,KAAK,GAAGA,KAAK,GAAG,KAAK,GAAG,IAAI;UAC5BD,IAAI,GAAG,EAAE;QACX;QAEA,oBACErC,OAAA;UAAeqB,KAAK,EAAE;YAAEO,MAAM,EAAE,OAAO;YAAEY,KAAK,EAAEL,KAAK,CAACK;UAAM,CAAE;UAAAb,QAAA,EAC3D,GAAGQ,KAAK,CAACI,OAAO,KAAKD,KAAK,GAAGD,IAAI;QAAE,GAD9BD,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC;MAER,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EACA,OAAO,IAAI;AACb,CAAC;AAACQ,EAAA,GApCIzB,aAAa;AAsCnB,eAAe,SAAS0B,gBAAgBA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACjD;EACA,MAAMC,eAAe,GAAGD,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,KAAK,cAAc,CAAC;;EAEhF;EACA,MAAMC,SAAS,GAAGJ,eAAe,CAACV,GAAG,CAACY,IAAI,KAAK;IAC7C,GAAGA,IAAI;IACPG,cAAc,EAAEH,IAAI,CAACI,eAAe,GAAG,CAAC,GAAG;EAC7C,CAAC,CAAC,CAAC;EAEH,oBACElD,OAAA;IAAKoB,SAAS,EAAC,eAAe;IAAAO,QAAA,gBAC5B3B,OAAA;MAAA2B,QAAA,EAAI;IAAsB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BjC,OAAA,CAACP,mBAAmB;MAAC0D,KAAK,EAAC,MAAM;MAACC,MAAM,EAAE,GAAI;MAAAzB,QAAA,eAC5C3B,OAAA,CAACL,aAAa;QACZgD,IAAI,EAAEK,SAAU;QAChBpB,MAAM,EAAE;UAAEyB,GAAG,EAAE,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBAEnD3B,OAAA,CAACX,KAAK;UACJkD,OAAO,EAAC,WAAW;UACnBkB,aAAa,EAAExD,gBAAiB;UAChCyD,KAAK,EAAE,CAAC,EAAG;UACXC,UAAU,EAAC,KAAK;UAChBP,MAAM,EAAE,EAAG;UACXQ,QAAQ,EAAC;QAAkB;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFjC,OAAA,CAACV,KAAK;UAACuE,OAAO,EAAC;QAAM;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBjC,OAAA,CAACV,KAAK;UAACuE,OAAO,EAAC,OAAO;UAACC,WAAW,EAAC,OAAO;UAACC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DjC,OAAA,CAACT,OAAO;UAACyE,OAAO,eAAEhE,OAAA,CAACgB,aAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvCjC,OAAA,CAACR,aAAa;UAACyE,MAAM,EAAC,MAAM;UAACC,eAAe,EAAC;QAAK;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjC,OAAA,CAACN,MAAM;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVjC,OAAA,CAACZ,IAAI;UACHyE,OAAO,EAAC,MAAM;UACdM,IAAI,EAAC,UAAU;UACf5B,OAAO,EAAC,aAAa;UACrB0B,MAAM,EAAC,SAAS;UAChBG,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEF,WAAW,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC/CC,IAAI,EAAC;QAAmB;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACFjC,OAAA,CAACZ,IAAI;UACHyE,OAAO,EAAC,MAAM;UACdM,IAAI,EAAC,UAAU;UACf5B,OAAO,EAAC,eAAe;UACvB0B,MAAM,EAAC,SAAS;UAChBG,WAAW,EAAE,CAAE;UACfC,GAAG,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEF,WAAW,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC/CC,IAAI,EAAC;QAAmB;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACFjC,OAAA,CAACJ,GAAG;UACFiE,OAAO,EAAC,OAAO;UACftB,OAAO,EAAC,gBAAgB;UACxB+B,IAAI,EAAC,SAAS;UACdE,IAAI,EAAC,iBAAiB;UACtBC,OAAO,EAAE;QAAI;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnB,CAAC;AAEV;AAACyC,GAAA,GA5DuBhC,gBAAgB;AAAA,IAAAD,EAAA,EAAAiC,GAAA;AAAAC,YAAA,CAAAlC,EAAA;AAAAkC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}