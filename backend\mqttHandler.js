const mqtt = require("mqtt");
const pool = require("./db");

const client = mqtt.connect("mqtt://broker.hivemq.com:1883");

client.on("connect", () => {
  console.log("MQTT connected");
  client.subscribe("iot/sensors");
});

client.on("message", async (topic, message) => {
  const payload = JSON.parse(message.toString());
  console.log("Received sensor data:", payload);

  try {
    // Extract common fields
    const { device_id, type: sensor_type, location, timestamp } = payload;

    // Prepare the base insert data
    const insertData = {
      device_id,
      sensor_type,
      location,
      raw_data: payload,
    };

    // Extract sensor-specific data based on sensor type
    switch (sensor_type) {
      case "environmental":
        insertData.temperature = payload.temperature;
        insertData.humidity = payload.humidity;
        break;

      case "air_quality":
        insertData.co2_level = payload.co2_level;
        insertData.pm2_5 = payload.pm2_5;
        insertData.air_quality_index = payload.air_quality_index;
        break;

      case "motion_light":
        insertData.motion_detected = payload.motion_detected;
        insertData.light_level = payload.light_level;
        insertData.battery_level = payload.battery_level;
        break;

      case "pressure_flow":
        insertData.pressure = payload.pressure;
        insertData.flow_rate = payload.flow_rate;
        insertData.vibration = payload.vibration;
        insertData.status = payload.status;
        break;

      default:
        console.log(`Unknown sensor type: ${sensor_type}`);
    }

    // Build dynamic SQL query
    const columns = Object.keys(insertData).join(", ");
    const placeholders = Object.keys(insertData)
      .map((_, index) => `$${index + 1}`)
      .join(", ");
    const values = Object.values(insertData);

    const query = `INSERT INTO sensor_data (${columns}) VALUES (${placeholders})`;

    await pool.query(query, values);
    console.log(`Successfully inserted data for ${device_id}`);
  } catch (err) {
    console.error("DB insert error:", err.message);
    console.error("Payload that caused error:", payload);
  }
});
