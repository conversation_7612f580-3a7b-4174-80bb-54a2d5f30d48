import paho.mqtt.client as mqtt
import random
import json
import time

broker = "broker.hivemq.com"  # or your local/cloud broker
port = 1883
topic = "iot/sensors"

client = mqtt.Client()
client.connect(broker, port)

# Define sensor configurations
sensors = [
    {
        "device_id": "sensor-001",
        "type": "environmental",
        "location": "office",
        "data_generator": lambda: {
            "temperature": round(random.uniform(20, 35), 2),
            "humidity": round(random.uniform(30, 70), 2),
        }
    },
    {
        "device_id": "sensor-002",
        "type": "air_quality",
        "location": "warehouse",
        "data_generator": lambda: {
            "co2_level": round(random.uniform(400, 1200), 1),
            "pm2_5": round(random.uniform(5, 50), 2),
            "air_quality_index": random.randint(50, 150),
        }
    },
    {
        "device_id": "sensor-003",
        "type": "motion_light",
        "location": "entrance",
        "data_generator": lambda: {
            "motion_detected": random.choice([True, False]),
            "light_level": round(random.uniform(0, 1000), 1),
            "battery_level": round(random.uniform(20, 100), 1),
        }
    },
    {
        "device_id": "sensor-004",
        "type": "pressure_flow",
        "location": "pipeline_a",
        "data_generator": lambda: {
            "pressure": round(random.uniform(1.2, 4.8), 2),
            "flow_rate": round(random.uniform(10, 85), 1),
            "vibration": round(random.uniform(0.1, 2.5), 3),
            "status": random.choice(["normal", "warning", "critical"]),
        }
    }
]

# Publish data from all sensors
sensor_index = 0
while True:
    # Cycle through sensors
    current_sensor = sensors[sensor_index]

    # Generate base data
    data = {
        "device_id": current_sensor["device_id"],
        "type": current_sensor["type"],
        "location": current_sensor["location"],
        "timestamp": int(time.time()),
    }

    # Add sensor-specific data
    sensor_data = current_sensor["data_generator"]()
    data.update(sensor_data)

    # Publish data
    client.publish(topic, json.dumps(data))
    print(f"Published from {current_sensor['device_id']}: {data}")

    # Move to next sensor
    sensor_index = (sensor_index + 1) % len(sensors)

    time.sleep(2)  # Reduced sleep time since we're cycling through sensors
