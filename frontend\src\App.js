import React, { useEffect, useState } from "react";
import axios from "axios";
import Chart from "./components/Chart";
import EnvironmentalChart from "./components/EnvironmentalChart";
import AirQualityChart from "./components/AirQualityChart";
import MotionLightChart from "./components/MotionLightChart";
import PressureFlowChart from "./components/PressureFlowChart";
import "./App.css";

function App() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sensorStats, setSensorStats] = useState([]);
  const [activeTab, setActiveTab] = useState("all");

  const fetchData = async () => {
    try {
      if (!loading) setIsRefreshing(true); // Only show refreshing indicator after initial load
      setError(null);

      // Fetch sensor data and stats in parallel
      const [dataResponse, statsResponse] = await Promise.all([
        axios.get("http://localhost:5000/api/data"),
        axios.get("http://localhost:5000/api/sensors"),
      ]);

      setData(dataResponse.data.reverse());
      setSensorStats(statsResponse.data);
      setLastUpdated(new Date());
      setLoading(false);
      setIsRefreshing(false);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError("Failed to fetch sensor data");
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    // Fetch data immediately when component mounts
    fetchData();

    // Set up interval to fetch data every 5 seconds
    const interval = setInterval(() => {
      fetchData();
    }, 5000);

    // Cleanup interval on component unmount
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="dashboard-container">
        <h2>IoT Sensor Dashboard</h2>
        <div className="chart-container">
          <p>Loading sensor data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-container">
        <h2>IoT Sensor Dashboard</h2>
        <div className="chart-container">
          <p style={{ color: "red" }}>{error}</p>
          <p>Data will automatically refresh every 5 seconds.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <h1>IoT Sensor Dashboard</h1>

      {/* Status Bar */}
      <div
        className="status-bar"
        style={{
          marginBottom: "20px",
          padding: "10px",
          backgroundColor: "#f5f5f5",
          borderRadius: "4px",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "wrap",
          }}
        >
          <div style={{ fontSize: "14px", color: "#666" }}>
            <span>
              {isRefreshing
                ? "🔄 Refreshing..."
                : "🔄 Auto-refreshing every 5 seconds"}
            </span>
            <span style={{ marginLeft: "20px" }}>
              Last updated:{" "}
              {lastUpdated ? lastUpdated.toLocaleTimeString() : "Loading..."}
            </span>
          </div>

          {/* Sensor Stats */}
          <div style={{ display: "flex", gap: "15px", flexWrap: "wrap" }}>
            {sensorStats.map((stat, index) => (
              <div
                key={index}
                style={{
                  padding: "5px 10px",
                  backgroundColor: "#007bff",
                  color: "white",
                  borderRadius: "4px",
                  fontSize: "12px",
                  fontWeight: "bold",
                }}
              >
                {stat.sensor_type}: {stat.device_count} device(s)
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation" style={{ marginBottom: "20px" }}>
        {[
          "all",
          "environmental",
          "air_quality",
          "motion_light",
          "pressure_flow",
        ].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              padding: "10px 20px",
              marginRight: "10px",
              backgroundColor: activeTab === tab ? "#007bff" : "#f8f9fa",
              color: activeTab === tab ? "white" : "#333",
              border: "1px solid #dee2e6",
              borderRadius: "4px",
              cursor: "pointer",
              textTransform: "capitalize",
            }}
          >
            {tab.replace("_", " ")}
          </button>
        ))}
      </div>

      {/* Charts */}
      <div className="charts-container">
        {(activeTab === "all" || activeTab === "environmental") && (
          <EnvironmentalChart data={data} />
        )}

        {(activeTab === "all" || activeTab === "air_quality") && (
          <AirQualityChart data={data} />
        )}

        {(activeTab === "all" || activeTab === "motion_light") && (
          <MotionLightChart data={data} />
        )}

        {(activeTab === "all" || activeTab === "pressure_flow") && (
          <PressureFlowChart data={data} />
        )}

        {/* Keep original chart for backward compatibility */}
        {activeTab === "all" && (
          <div className="chart-section">
            <h3>Legacy Environmental Chart</h3>
            <Chart data={data} />
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
