[{"D:\\MobioProjects\\iot-poc\\frontend\\src\\index.js": "1", "D:\\MobioProjects\\iot-poc\\frontend\\src\\App.js": "2", "D:\\MobioProjects\\iot-poc\\frontend\\src\\reportWebVitals.js": "3", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\Chart.js": "4", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\EnvironmentalChart.js": "5", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\MotionLightChart.js": "6", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\PressureFlowChart.js": "7", "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\AirQualityChart.js": "8"}, {"size": 535, "mtime": 1750655226004, "results": "9", "hashOfConfig": "10"}, {"size": 5839, "mtime": 1750657639936, "results": "11", "hashOfConfig": "10"}, {"size": 362, "mtime": 1750655243006, "results": "12", "hashOfConfig": "10"}, {"size": 2656, "mtime": 1750656459757, "results": "13", "hashOfConfig": "10"}, {"size": 2919, "mtime": 1750657499495, "results": "14", "hashOfConfig": "10"}, {"size": 3753, "mtime": 1750657545258, "results": "15", "hashOfConfig": "10"}, {"size": 4347, "mtime": 1750657570600, "results": "16", "hashOfConfig": "10"}, {"size": 3421, "mtime": 1750657522158, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "vgru56", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\MobioProjects\\iot-poc\\frontend\\src\\index.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\App.js", ["42"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\reportWebVitals.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\Chart.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\EnvironmentalChart.js", [], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\MotionLightChart.js", ["43", "44", "45"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\PressureFlowChart.js", ["46", "47"], [], "D:\\MobioProjects\\iot-poc\\frontend\\src\\components\\AirQualityChart.js", ["48"], [], {"ruleId": "49", "severity": 1, "message": "50", "line": 54, "column": 6, "nodeType": "51", "endLine": 54, "endColumn": 8, "suggestions": "52"}, {"ruleId": "53", "severity": 1, "message": "54", "line": 2, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 2, "endColumn": 12}, {"ruleId": "53", "severity": 1, "message": "57", "line": 12, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 12, "endColumn": 15}, {"ruleId": "53", "severity": 1, "message": "58", "line": 13, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 13, "endColumn": 10}, {"ruleId": "53", "severity": 1, "message": "54", "line": 2, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 2, "endColumn": 12}, {"ruleId": "53", "severity": 1, "message": "59", "line": 11, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 11, "endColumn": 6}, {"ruleId": "53", "severity": 1, "message": "54", "line": 2, "column": 3, "nodeType": "55", "messageId": "56", "endLine": 2, "endColumn": 12}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["60"], "no-unused-vars", "'LineChart' is defined but never used.", "Identifier", "unusedVar", "'ScatterChart' is defined but never used.", "'Scatter' is defined but never used.", "'Bar' is defined but never used.", {"desc": "61", "fix": "62"}, "Update the dependencies array to be: [fetchData]", {"range": "63", "text": "64"}, [1882, 1884], "[fetchData]"]