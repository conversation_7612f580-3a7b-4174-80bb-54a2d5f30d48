{"ast": null, "code": "var _jsxFileName = \"D:\\\\MobioProjects\\\\iot-poc\\\\frontend\\\\src\\\\components\\\\Chart.js\";\nimport React from \"react\";\nimport { LineChart, Line, XAxis, YAxis, Tooltip, CartesianGrid, ResponsiveContainer } from \"recharts\";\n\n// Function to format timestamp for X-axis display\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatXAxisLabel = tickItem => {\n  const date = new Date(tickItem);\n  return date.toLocaleTimeString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Function to format timestamp for tooltip\nconst formatTooltipLabel = label => {\n  const date = new Date(label);\n  return date.toLocaleString(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: false\n  });\n};\n\n// Custom tooltip component\nconst CustomTooltip = ({\n  active,\n  payload,\n  label\n}) => {\n  if (active && payload && payload.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"custom-tooltip\",\n      style: {\n        backgroundColor: \"white\",\n        padding: \"10px\",\n        border: \"1px solid #ccc\",\n        borderRadius: \"4px\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: \"0 0 5px 0\",\n          fontWeight: \"bold\"\n        },\n        children: formatTooltipLabel(label)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: \"2px 0\",\n          color: entry.color\n        },\n        children: `${entry.dataKey}: ${entry.value}${entry.dataKey === \"temperature\" ? \"°C\" : \"%\"}`\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n  return null;\n};\n_c = CustomTooltip;\nexport default function Chart({\n  data\n}) {\n  return /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n    width: \"100%\",\n    height: 400,\n    children: /*#__PURE__*/_jsxDEV(LineChart, {\n      data: data,\n      margin: {\n        top: 5,\n        right: 30,\n        left: 20,\n        bottom: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(XAxis, {\n        dataKey: \"timestamp\",\n        tickFormatter: formatXAxisLabel,\n        angle: -45,\n        textAnchor: \"end\",\n        height: 60,\n        interval: \"preserveStartEnd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 27\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n        stroke: \"#ccc\",\n        strokeDasharray: \"3 3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Line, {\n        type: \"monotone\",\n        dataKey: \"temperature\",\n        stroke: \"#ff7300\",\n        strokeWidth: 2,\n        dot: {\n          fill: \"#ff7300\",\n          strokeWidth: 2,\n          r: 4\n        },\n        name: \"Temperature (\\xB0C)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Line, {\n        type: \"monotone\",\n        dataKey: \"humidity\",\n        stroke: \"#387908\",\n        strokeWidth: 2,\n        dot: {\n          fill: \"#387908\",\n          strokeWidth: 2,\n          r: 4\n        },\n        name: \"Humidity (%)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n}\n_c2 = Chart;\nvar _c, _c2;\n$RefreshReg$(_c, \"CustomTooltip\");\n$RefreshReg$(_c2, \"Chart\");", "map": {"version": 3, "names": ["React", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Cartesian<PERSON><PERSON>", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "formatXAxisLabel", "tickItem", "date", "Date", "toLocaleTimeString", "hour", "minute", "hour12", "formatTooltipLabel", "label", "toLocaleString", "year", "month", "day", "second", "CustomTooltip", "active", "payload", "length", "className", "style", "backgroundColor", "padding", "border", "borderRadius", "boxShadow", "children", "margin", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "entry", "index", "color", "dataKey", "value", "_c", "Chart", "data", "width", "height", "top", "right", "left", "bottom", "tick<PERSON><PERSON><PERSON><PERSON>", "angle", "textAnchor", "interval", "content", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "strokeWidth", "dot", "fill", "r", "name", "_c2", "$RefreshReg$"], "sources": ["D:/MobioProjects/iot-poc/frontend/src/components/Chart.js"], "sourcesContent": ["import React from \"react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>,\r\n  XAxis,\r\n  <PERSON>Axis,\r\n  Tooltip,\r\n  CartesianGrid,\r\n  ResponsiveContainer,\r\n} from \"recharts\";\r\n\r\n// Function to format timestamp for X-axis display\r\nconst formatXAxisLabel = (tickItem) => {\r\n  const date = new Date(tickItem);\r\n  return date.toLocaleTimeString(\"en-US\", {\r\n    hour: \"2-digit\",\r\n    minute: \"2-digit\",\r\n    hour12: false,\r\n  });\r\n};\r\n\r\n// Function to format timestamp for tooltip\r\nconst formatTooltipLabel = (label) => {\r\n  const date = new Date(label);\r\n  return date.toLocaleString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"short\",\r\n    day: \"numeric\",\r\n    hour: \"2-digit\",\r\n    minute: \"2-digit\",\r\n    second: \"2-digit\",\r\n    hour12: false,\r\n  });\r\n};\r\n\r\n// Custom tooltip component\r\nconst CustomTooltip = ({ active, payload, label }) => {\r\n  if (active && payload && payload.length) {\r\n    return (\r\n      <div\r\n        className=\"custom-tooltip\"\r\n        style={{\r\n          backgroundColor: \"white\",\r\n          padding: \"10px\",\r\n          border: \"1px solid #ccc\",\r\n          borderRadius: \"4px\",\r\n          boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\r\n        }}\r\n      >\r\n        <p style={{ margin: \"0 0 5px 0\", fontWeight: \"bold\" }}>\r\n          {formatTooltipLabel(label)}\r\n        </p>\r\n        {payload.map((entry, index) => (\r\n          <p key={index} style={{ margin: \"2px 0\", color: entry.color }}>\r\n            {`${entry.dataKey}: ${entry.value}${\r\n              entry.dataKey === \"temperature\" ? \"°C\" : \"%\"\r\n            }`}\r\n          </p>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nexport default function Chart({ data }) {\r\n  return (\r\n    <ResponsiveContainer width=\"100%\" height={400}>\r\n      <LineChart\r\n        data={data}\r\n        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\r\n      >\r\n        <XAxis\r\n          dataKey=\"timestamp\"\r\n          tickFormatter={formatXAxisLabel}\r\n          angle={-45}\r\n          textAnchor=\"end\"\r\n          height={60}\r\n          interval=\"preserveStartEnd\"\r\n        />\r\n        <YAxis />\r\n        <Tooltip content={<CustomTooltip />} />\r\n        <CartesianGrid stroke=\"#ccc\" strokeDasharray=\"3 3\" />\r\n        <Line\r\n          type=\"monotone\"\r\n          dataKey=\"temperature\"\r\n          stroke=\"#ff7300\"\r\n          strokeWidth={2}\r\n          dot={{ fill: \"#ff7300\", strokeWidth: 2, r: 4 }}\r\n          name=\"Temperature (°C)\"\r\n        />\r\n        <Line\r\n          type=\"monotone\"\r\n          dataKey=\"humidity\"\r\n          stroke=\"#387908\"\r\n          strokeWidth={2}\r\n          dot={{ fill: \"#387908\", strokeWidth: 2, r: 4 }}\r\n          name=\"Humidity (%)\"\r\n        />\r\n      </LineChart>\r\n    </ResponsiveContainer>\r\n  );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,mBAAmB,QACd,UAAU;;AAEjB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;EACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,QAAQ,CAAC;EAC/B,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;EACpC,MAAMP,IAAI,GAAG,IAAIC,IAAI,CAACM,KAAK,CAAC;EAC5B,OAAOP,IAAI,CAACQ,cAAc,CAAC,OAAO,EAAE;IAClCC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,SAAS;IACdR,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBQ,MAAM,EAAE,SAAS;IACjBP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMQ,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAER;AAAM,CAAC,KAAK;EACpD,IAAIO,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;IACvC,oBACEnB,OAAA;MACEoB,SAAS,EAAC,gBAAgB;MAC1BC,KAAK,EAAE;QACLC,eAAe,EAAE,OAAO;QACxBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEF3B,OAAA;QAAGqB,KAAK,EAAE;UAAEO,MAAM,EAAE,WAAW;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDlB,kBAAkB,CAACC,KAAK;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,EACHf,OAAO,CAACgB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACxBpC,OAAA;QAAeqB,KAAK,EAAE;UAAEO,MAAM,EAAE,OAAO;UAAES,KAAK,EAAEF,KAAK,CAACE;QAAM,CAAE;QAAAV,QAAA,EAC3D,GAAGQ,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACI,KAAK,GAC/BJ,KAAK,CAACG,OAAO,KAAK,aAAa,GAAG,IAAI,GAAG,GAAG;MAC5C,GAHIF,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIV,CACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EACA,OAAO,IAAI;AACb,CAAC;AAACO,EAAA,GA3BIxB,aAAa;AA6BnB,eAAe,SAASyB,KAAKA,CAAC;EAAEC;AAAK,CAAC,EAAE;EACtC,oBACE1C,OAAA,CAACF,mBAAmB;IAAC6C,KAAK,EAAC,MAAM;IAACC,MAAM,EAAE,GAAI;IAAAjB,QAAA,eAC5C3B,OAAA,CAACR,SAAS;MACRkD,IAAI,EAAEA,IAAK;MACXd,MAAM,EAAE;QAAEiB,GAAG,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAE;MAAArB,QAAA,gBAEnD3B,OAAA,CAACN,KAAK;QACJ4C,OAAO,EAAC,WAAW;QACnBW,aAAa,EAAEhD,gBAAiB;QAChCiD,KAAK,EAAE,CAAC,EAAG;QACXC,UAAU,EAAC,KAAK;QAChBP,MAAM,EAAE,EAAG;QACXQ,QAAQ,EAAC;MAAkB;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACFjC,OAAA,CAACL,KAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACTjC,OAAA,CAACJ,OAAO;QAACyD,OAAO,eAAErD,OAAA,CAACgB,aAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCjC,OAAA,CAACH,aAAa;QAACyD,MAAM,EAAC,MAAM;QAACC,eAAe,EAAC;MAAK;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrDjC,OAAA,CAACP,IAAI;QACH+D,IAAI,EAAC,UAAU;QACflB,OAAO,EAAC,aAAa;QACrBgB,MAAM,EAAC,SAAS;QAChBG,WAAW,EAAE,CAAE;QACfC,GAAG,EAAE;UAAEC,IAAI,EAAE,SAAS;UAAEF,WAAW,EAAE,CAAC;UAAEG,CAAC,EAAE;QAAE,CAAE;QAC/CC,IAAI,EAAC;MAAkB;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFjC,OAAA,CAACP,IAAI;QACH+D,IAAI,EAAC,UAAU;QACflB,OAAO,EAAC,UAAU;QAClBgB,MAAM,EAAC,SAAS;QAChBG,WAAW,EAAE,CAAE;QACfC,GAAG,EAAE;UAAEC,IAAI,EAAE,SAAS;UAAEF,WAAW,EAAE,CAAC;UAAEG,CAAC,EAAE;QAAE,CAAE;QAC/CC,IAAI,EAAC;MAAc;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAE1B;AAAC6B,GAAA,GArCuBrB,KAAK;AAAA,IAAAD,EAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAAvB,EAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}